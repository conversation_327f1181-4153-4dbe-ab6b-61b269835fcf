#!/usr/bin/env python3
"""
Validation script for generated AGI scenario files.
Tests that the generated AGI scenarios are properly formatted and executable.
"""

import json
import numpy as np
from pathlib import Path
import time
from typing import Dict, List, Tuple
import argparse
from tqdm import tqdm
import re

import sys
sys.path.append('..')
from src.command_executor import CommandExecutor

class AGIScenarioValidator:
    """Validator for AGI scenario files."""
    
    def __init__(self, agi_dir: str = "../arcdata/evaluation/agi_files"):
        self.agi_dir = Path(agi_dir)
        self.command_executor = CommandExecutor()
        
        self.validation_results = {
            "total_files": 0,
            "valid_format": 0,
            "valid_syntax": 0,
            "executable": 0,
            "file_results": [],
            "error_summary": {},
            "command_analysis": {}
        }
    
    def validate_all_scenarios(self) -> Dict:
        """Validate all AGI scenario files."""
        print("🔍 VALIDATING GENERATED AGI SCENARIOS")
        print("=" * 50)
        print(f"AGI Directory: {self.agi_dir}")
        
        # Get all AGI files
        agi_files = list(self.agi_dir.glob("*.agi"))
        self.validation_results["total_files"] = len(agi_files)
        
        print(f"Found {len(agi_files)} AGI files to validate")
        print()
        
        # Validate each file
        for agi_file in tqdm(agi_files, desc="Validating AGI files"):
            result = self._validate_single_file(agi_file)
            self.validation_results["file_results"].append(result)
        
        # Compute summary statistics
        self._compute_validation_statistics()
        
        # Generate validation report
        self._generate_validation_report()
        
        return self.validation_results
    
    def _validate_single_file(self, agi_file: Path) -> Dict:
        """Validate a single AGI scenario file."""
        puzzle_id = agi_file.stem
        
        try:
            # Read file content
            with open(agi_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Validate file format
            format_result = self._validate_file_format(content)
            
            # Extract program from content
            program = self._extract_program(content)
            
            # Validate program syntax
            syntax_result = self._validate_program_syntax(program)
            
            # Test executability (if syntax is valid)
            execution_result = None
            if syntax_result["valid"] and program:
                execution_result = self._test_program_execution(program)
            
            # Update counters
            if format_result["valid"]:
                self.validation_results["valid_format"] += 1
            
            if syntax_result["valid"]:
                self.validation_results["valid_syntax"] += 1
            
            if execution_result and execution_result["executable"]:
                self.validation_results["executable"] += 1
            
            return {
                "puzzle_id": puzzle_id,
                "file_path": str(agi_file),
                "format_validation": format_result,
                "syntax_validation": syntax_result,
                "execution_validation": execution_result,
                "program": program
            }
            
        except Exception as e:
            return {
                "puzzle_id": puzzle_id,
                "file_path": str(agi_file),
                "error": str(e),
                "format_validation": {"valid": False, "errors": [f"File read error: {e}"]},
                "syntax_validation": {"valid": False, "errors": []},
                "execution_validation": None,
                "program": None
            }
    
    def _validate_file_format(self, content: str) -> Dict:
        """Validate AGI file format."""
        errors = []
        
        # Check for required sections
        if "# ARC Puzzle" not in content:
            errors.append("Missing puzzle header")
        
        if "# Generated by" not in content:
            errors.append("Missing generation info")
        
        if "# Generated Program:" not in content and "Generated Program:" not in content:
            errors.append("Missing program section")
        
        # Check for basic structure
        lines = content.split('\n')
        if len(lines) < 5:
            errors.append("File too short")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "line_count": len(lines)
        }
    
    def _extract_program(self, content: str) -> str:
        """Extract the program from AGI file content."""
        lines = content.split('\n')
        program_lines = []
        in_program_section = False
        
        for line in lines:
            if "# Generated Program:" in line or "Generated Program:" in line:
                in_program_section = True
                continue
            
            if in_program_section:
                # Skip empty lines and comments
                if line.strip() and not line.strip().startswith('#'):
                    program_lines.append(line.strip())
        
        return '\n'.join(program_lines)
    
    def _validate_program_syntax(self, program: str) -> Dict:
        """Validate program syntax."""
        if not program or not program.strip():
            return {"valid": False, "errors": ["Empty program"]}
        
        errors = []
        commands_found = []
        
        # Valid command patterns
        valid_commands = ['INIT', 'FILL', 'COPY', 'MOVE', 'ROTATE', 'REFLECT', 'SCALE', 'CROP', 'EDIT', 'MOTIF', 'PASTE']
        
        lines = program.strip().split('\n')
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
            
            # Extract command (first word)
            parts = line.split()
            if parts:
                command = parts[0]
                commands_found.append(command)
                
                # Check if command is valid
                if not any(command.startswith(valid_cmd) for valid_cmd in valid_commands):
                    errors.append(f"Line {i+1}: Unknown command '{command}'")
        
        # Analyze command distribution
        command_counts = {}
        for cmd in commands_found:
            command_counts[cmd] = command_counts.get(cmd, 0) + 1
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "commands_found": commands_found,
            "command_counts": command_counts,
            "total_commands": len(commands_found)
        }
    
    def _test_program_execution(self, program: str) -> Dict:
        """Test if program can be executed."""
        try:
            # Create a simple test grid
            test_grid = [[0, 1], [1, 0]]
            
            # Try to execute the program
            commands = program.strip().split('\n') if program else []
            
            # Basic execution test (without full execution)
            executable = True
            execution_errors = []
            
            for i, command in enumerate(commands):
                if not command.strip():
                    continue
                
                # Basic command format validation
                parts = command.strip().split()
                if not parts:
                    executable = False
                    execution_errors.append(f"Line {i+1}: Empty command")
                    continue
                
                # Check INIT command format
                if parts[0] == 'INIT':
                    if len(parts) < 2:
                        executable = False
                        execution_errors.append(f"Line {i+1}: INIT missing parameters")
            
            return {
                "executable": executable,
                "errors": execution_errors,
                "command_count": len([c for c in commands if c.strip()])
            }
            
        except Exception as e:
            return {
                "executable": False,
                "errors": [f"Execution test failed: {e}"],
                "command_count": 0
            }
    
    def _compute_validation_statistics(self):
        """Compute validation statistics."""
        total = self.validation_results["total_files"]
        
        if total > 0:
            self.validation_results["format_success_rate"] = self.validation_results["valid_format"] / total
            self.validation_results["syntax_success_rate"] = self.validation_results["valid_syntax"] / total
            self.validation_results["execution_success_rate"] = self.validation_results["executable"] / total
        
        # Analyze common errors
        error_counts = {}
        command_stats = {}
        
        for result in self.validation_results["file_results"]:
            # Format errors
            if result.get("format_validation", {}).get("errors"):
                for error in result["format_validation"]["errors"]:
                    error_counts[f"Format: {error}"] = error_counts.get(f"Format: {error}", 0) + 1
            
            # Syntax errors
            if result.get("syntax_validation", {}).get("errors"):
                for error in result["syntax_validation"]["errors"]:
                    error_counts[f"Syntax: {error}"] = error_counts.get(f"Syntax: {error}", 0) + 1
            
            # Command statistics
            if result.get("syntax_validation", {}).get("command_counts"):
                for cmd, count in result["syntax_validation"]["command_counts"].items():
                    command_stats[cmd] = command_stats.get(cmd, 0) + count
        
        self.validation_results["error_summary"] = error_counts
        self.validation_results["command_analysis"] = command_stats
    
    def _generate_validation_report(self):
        """Generate validation report."""
        report_lines = []
        
        report_lines.append("# AGI SCENARIO VALIDATION REPORT")
        report_lines.append("=" * 50)
        report_lines.append("")
        report_lines.append(f"**Validation Date:** {time.strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"**AGI Directory:** {self.agi_dir}")
        report_lines.append("")
        
        # Summary statistics
        total = self.validation_results["total_files"]
        report_lines.append("## VALIDATION SUMMARY")
        report_lines.append(f"- **Total AGI Files:** {total}")
        report_lines.append(f"- **Valid Format:** {self.validation_results['valid_format']} ({self.validation_results.get('format_success_rate', 0):.1%})")
        report_lines.append(f"- **Valid Syntax:** {self.validation_results['valid_syntax']} ({self.validation_results.get('syntax_success_rate', 0):.1%})")
        report_lines.append(f"- **Executable:** {self.validation_results['executable']} ({self.validation_results.get('execution_success_rate', 0):.1%})")
        report_lines.append("")
        
        # Command analysis
        if self.validation_results["command_analysis"]:
            report_lines.append("## COMMAND ANALYSIS")
            for cmd, count in sorted(self.validation_results["command_analysis"].items(), 
                                   key=lambda x: x[1], reverse=True):
                report_lines.append(f"- **{cmd}:** {count} occurrences")
            report_lines.append("")
        
        # Error summary
        if self.validation_results["error_summary"]:
            report_lines.append("## COMMON ERRORS")
            for error, count in sorted(self.validation_results["error_summary"].items(), 
                                     key=lambda x: x[1], reverse=True)[:10]:
                report_lines.append(f"- {error}: {count} files")
            report_lines.append("")
        
        # Conclusion
        report_lines.append("## CONCLUSION")
        success_rate = self.validation_results.get('syntax_success_rate', 0)
        if success_rate > 0.8:
            report_lines.append("✅ **Excellent:** High quality AGI scenario generation")
        elif success_rate > 0.6:
            report_lines.append("✅ **Good:** Satisfactory AGI scenario generation")
        elif success_rate > 0.4:
            report_lines.append("⚠️ **Fair:** AGI scenarios need improvement")
        else:
            report_lines.append("❌ **Poor:** Significant issues with AGI scenario generation")
        
        # Save report
        report_file = self.agi_dir.parent / "agi_validation_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        print(f"\n📊 Validation report saved: {report_file}")


def main():
    """Main function for AGI scenario validation."""
    parser = argparse.ArgumentParser(description="Validate generated AGI scenario files")
    parser.add_argument("--agi-dir", default="../arcdata/evaluation/agi_files",
                       help="Directory containing AGI files")
    
    args = parser.parse_args()
    
    # Run validation
    validator = AGIScenarioValidator(args.agi_dir)
    results = validator.validate_all_scenarios()
    
    # Print summary
    print(f"\n✅ VALIDATION COMPLETED")
    print(f"Total Files: {results['total_files']}")
    print(f"Valid Format: {results['valid_format']} ({results.get('format_success_rate', 0):.1%})")
    print(f"Valid Syntax: {results['valid_syntax']} ({results.get('syntax_success_rate', 0):.1%})")
    print(f"Executable: {results['executable']} ({results.get('execution_success_rate', 0):.1%})")


if __name__ == "__main__":
    main()
