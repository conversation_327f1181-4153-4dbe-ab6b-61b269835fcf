#!/usr/bin/env python3
"""
Détecteur MOSAIC amélioré avec critères stricts
Basé sur l'analyse des vrais patterns de mosaïque
"""

import json
import os
import numpy as np
from collections import Counter
from typing import Dict, List, Tuple
from analyze_grid.arc_analyzer import ARCAnalyzer

class ImprovedMosaicDetector:
    """Détecteur MOSAIC avec critères affinés"""
    
    def __init__(self):
        self.analyzer = ARCAnalyzer()
    
    def analyze_color_usage(self, grid: np.ndarray) -> Dict:
        """Analyse l'utilisation des couleurs dans une grille"""
        
        total_cells = grid.size
        color_counts = Counter(grid.flatten())
        
        # Retirer le fond noir pour l'analyse
        if 0 in color_counts:
            background_cells = color_counts[0]
            del color_counts[0]
        else:
            background_cells = 0
        
        non_background_cells = total_cells - background_cells
        
        analysis = {
            'total_cells': total_cells,
            'background_cells': background_cells,
            'non_background_cells': non_background_cells,
            'unique_colors': len(color_counts),
            'color_counts': dict(color_counts),
            'color_usage_ratio': non_background_cells / total_cells if total_cells > 0 else 0,
            'color_balance_score': 0.0,
            'dominant_color_ratio': 0.0
        }
        
        # Calculer la distribution des couleurs
        color_distribution = {}
        if non_background_cells > 0:
            for color, count in color_counts.items():
                color_distribution[color] = count / non_background_cells
        
        # Calculer le score d'équilibre des couleurs
        if len(color_counts) > 0:
            color_ratios = list(color_distribution.values())
            
            # Score d'équilibre : plus les couleurs sont équilibrées, plus le score est élevé
            if len(color_ratios) > 1:
                # Variance des ratios (plus faible = plus équilibré)
                variance = np.var(color_ratios)
                # Score inversé : variance faible = score élevé
                analysis['color_balance_score'] = 1.0 / (1.0 + variance * 10)
            else:
                analysis['color_balance_score'] = 0.0
            
            # Ratio de la couleur dominante
            analysis['dominant_color_ratio'] = max(color_ratios)
        
        return analysis
    
    def calculate_improved_mosaic_score(self, puzzle_data: Dict, puzzle_id: str) -> Dict:
        """Calcule un score MOSAIC amélioré avec critères stricts"""
        
        # Analyser chaque exemple
        examples_analysis = []
        
        for i, example in enumerate(puzzle_data['train']):
            input_grid = np.array(example['input'])
            h, w = input_grid.shape
            
            # Analyse des couleurs
            color_analysis = self.analyze_color_usage(input_grid)
            
            example_metrics = {
                'dimensions': (h, w),
                'area': h * w,
                'color_analysis': color_analysis
            }
            
            examples_analysis.append(example_metrics)
        
        # Métriques globales
        max_area = max(ex['area'] for ex in examples_analysis)
        avg_area = np.mean([ex['area'] for ex in examples_analysis])
        max_colors = max(ex['color_analysis']['unique_colors'] for ex in examples_analysis)
        avg_colors = np.mean([ex['color_analysis']['unique_colors'] for ex in examples_analysis])
        
        # Métriques d'utilisation des couleurs
        avg_usage_ratio = np.mean([ex['color_analysis']['color_usage_ratio'] for ex in examples_analysis])
        avg_balance_score = np.mean([ex['color_analysis']['color_balance_score'] for ex in examples_analysis])
        avg_dominant_ratio = np.mean([ex['color_analysis']['dominant_color_ratio'] for ex in examples_analysis])
        
        # Analyse avec ARCAnalyzer pour les symétries
        analysis = self.analyzer.analyze_puzzle(puzzle_data)
        symmetries = analysis.get('symmetries', {})
        
        # Compter les symétries
        symmetry_count = 0
        if 'input_symmetries' in symmetries:
            for sym in symmetries['input_symmetries']:
                if sym.get('horizontal', False): symmetry_count += 1
                if sym.get('vertical', False): symmetry_count += 1
                if sym.get('diagonal_main', False): symmetry_count += 1
                if sym.get('diagonal_anti', False): symmetry_count += 1
        
        # CRITÈRES MOSAIC STRICTS
        score = 0.0
        criteria_met = {}
        
        # 1. Grande taille (poids: 0.25)
        size_score = 0.0
        if max_area >= 400:  # 20x20+
            size_score = 1.0
        elif max_area >= 225:  # 15x15+
            size_score = 0.8
        elif max_area >= 144:  # 12x12+
            size_score = 0.6
        elif max_area >= 100:  # 10x10+
            size_score = 0.4
        
        criteria_met['large_size'] = size_score > 0.6
        score += size_score * 0.25
        
        # 2. Nombreuses couleurs (poids: 0.20)
        color_count_score = 0.0
        if max_colors >= 8:
            color_count_score = 1.0
        elif max_colors >= 7:
            color_count_score = 0.8
        elif max_colors >= 6:
            color_count_score = 0.6
        elif max_colors >= 5:
            color_count_score = 0.4
        
        criteria_met['many_colors'] = color_count_score > 0.6
        score += color_count_score * 0.20
        
        # 3. NOUVEAU: Utilisation intensive des couleurs (poids: 0.25)
        usage_score = 0.0
        if avg_usage_ratio >= 0.8:  # 80%+ des cellules colorées
            usage_score = 1.0
        elif avg_usage_ratio >= 0.6:  # 60%+
            usage_score = 0.8
        elif avg_usage_ratio >= 0.4:  # 40%+
            usage_score = 0.6
        elif avg_usage_ratio >= 0.2:  # 20%+
            usage_score = 0.3
        
        criteria_met['high_color_usage'] = avg_usage_ratio >= 0.6
        score += usage_score * 0.25
        
        # 4. NOUVEAU: Équilibre des couleurs (poids: 0.20)
        balance_score = 0.0
        if avg_balance_score >= 0.8:  # Très équilibré
            balance_score = 1.0
        elif avg_balance_score >= 0.6:  # Bien équilibré
            balance_score = 0.8
        elif avg_balance_score >= 0.4:  # Moyennement équilibré
            balance_score = 0.5
        
        criteria_met['balanced_colors'] = avg_balance_score >= 0.6
        score += balance_score * 0.20
        
        # 5. NOUVEAU: Pas de couleur trop dominante (poids: 0.10)
        dominance_score = 0.0
        if avg_dominant_ratio <= 0.3:  # Aucune couleur > 30%
            dominance_score = 1.0
        elif avg_dominant_ratio <= 0.5:  # Aucune couleur > 50%
            dominance_score = 0.7
        elif avg_dominant_ratio <= 0.7:  # Aucune couleur > 70%
            dominance_score = 0.4
        
        criteria_met['no_dominant_color'] = avg_dominant_ratio <= 0.5
        score += dominance_score * 0.10
        
        # PÉNALITÉS
        # Pénalité pour symétries (les mosaïques sont généralement asymétriques)
        if symmetry_count > 0:
            score *= 0.8  # Réduction de 20%
            criteria_met['asymmetric'] = False
        else:
            criteria_met['asymmetric'] = True
        
        # Critères obligatoires pour être considéré comme MOSAIC
        mandatory_criteria = [
            criteria_met['large_size'],
            criteria_met['many_colors'],
            criteria_met['high_color_usage']
        ]
        
        is_mosaic = all(mandatory_criteria) and score >= 0.7
        
        return {
            'puzzle_id': puzzle_id,
            'mosaic_score': min(score, 1.0),
            'is_mosaic': is_mosaic,
            'criteria_met': criteria_met,
            'metrics': {
                'max_area': int(max_area),
                'avg_area': float(avg_area),
                'max_colors': int(max_colors),
                'avg_colors': float(avg_colors),
                'avg_usage_ratio': float(avg_usage_ratio),
                'avg_balance_score': float(avg_balance_score),
                'avg_dominant_ratio': float(avg_dominant_ratio),
                'symmetry_count': symmetry_count
            },
            'examples_analysis': examples_analysis
        }
    
    def scan_puzzles(self, data_dir: str = "../arcdata/training") -> Tuple[List[Dict], List[Dict]]:
        """Scanne tous les puzzles avec les critères améliorés"""
        
        print("🎨 DÉTECTION MOSAIC AMÉLIORÉE")
        print("=" * 50)
        print("Critères stricts:")
        print("✓ Grande taille (≥ 12x12)")
        print("✓ Nombreuses couleurs (≥ 6)")
        print("✓ Utilisation intensive (≥ 60% cellules colorées)")
        print("✓ Couleurs équilibrées (score ≥ 0.6)")
        print("✓ Pas de couleur dominante (< 50%)")
        print("✓ Asymétrique (pas de symétries)")
        print()
        
        mosaic_puzzles = []
        rejected_puzzles = []
        total_puzzles = 0
        
        for filename in os.listdir(data_dir):
            if not filename.endswith('.json'):
                continue
                
            puzzle_id = filename.replace('.json', '')
            filepath = os.path.join(data_dir, filename)
            
            try:
                with open(filepath, 'r') as f:
                    puzzle_data = json.load(f)
                
                result = self.calculate_improved_mosaic_score(puzzle_data, puzzle_id)
                total_puzzles += 1
                
                if result['is_mosaic']:
                    mosaic_puzzles.append(result)
                    print(f"✅ MOSAIC: {puzzle_id} (score: {result['mosaic_score']:.3f})")
                    
                    metrics = result['metrics']
                    print(f"   {metrics['max_area']} cellules, {metrics['max_colors']} couleurs")
                    print(f"   Usage: {metrics['avg_usage_ratio']:.1%}, Balance: {metrics['avg_balance_score']:.3f}")
                    print(f"   Critères: {sum(result['criteria_met'].values())}/6")
                    print()
                else:
                    # Garder les puzzles rejetés pour analyse
                    if result['mosaic_score'] > 0.5:  # Candidats proches
                        rejected_puzzles.append(result)
                
            except Exception as e:
                print(f"❌ Erreur avec {puzzle_id}: {e}")
                continue
        
        # Trier par score
        mosaic_puzzles.sort(key=lambda x: x['mosaic_score'], reverse=True)
        rejected_puzzles.sort(key=lambda x: x['mosaic_score'], reverse=True)
        
        print(f"📊 RÉSULTATS AMÉLIORÉS")
        print(f"Total puzzles: {total_puzzles}")
        print(f"MOSAIC détectés: {len(mosaic_puzzles)}")
        print(f"Candidats rejetés: {len(rejected_puzzles)}")
        print(f"Pourcentage MOSAIC: {len(mosaic_puzzles)/total_puzzles*100:.1f}%")
        
        # Afficher les rejetés pour comparaison
        if rejected_puzzles:
            print(f"\n🚫 TOP CANDIDATS REJETÉS:")
            for rej in rejected_puzzles[:5]:
                print(f"   {rej['puzzle_id']} (score: {rej['mosaic_score']:.3f}) - Critères: {sum(rej['criteria_met'].values())}/6")
        
        return mosaic_puzzles, rejected_puzzles
    
    def save_results(self, mosaic_puzzles: List[Dict], rejected_puzzles: List[Dict]):
        """Sauvegarde les résultats améliorés"""
        
        # IDs seulement pour usage facile
        mosaic_ids = [p['puzzle_id'] for p in mosaic_puzzles]
        
        with open("improved_mosaic_ids.txt", "w") as f:
            f.write("# PUZZLES MOSAIC - DÉTECTION AMÉLIORÉE\n")
            f.write(f"# Total: {len(mosaic_ids)} puzzles\n")
            f.write("# Critères stricts: Taille + Couleurs + Usage + Équilibre + Asymétrie\n\n")
            
            for puzzle in mosaic_puzzles:
                metrics = puzzle['metrics']
                f.write(f"{puzzle['puzzle_id']} # score:{puzzle['mosaic_score']:.3f} ")
                f.write(f"{metrics['max_area']}cells {metrics['max_colors']}colors ")
                f.write(f"usage:{metrics['avg_usage_ratio']:.1%} balance:{metrics['avg_balance_score']:.2f}\n")
        
        # Données complètes
        results = {
            'detection_method': 'improved_strict_criteria',
            'total_mosaic': len(mosaic_puzzles),
            'total_rejected': len(rejected_puzzles),
            'mosaic_ids': mosaic_ids,
            'mosaic_puzzles': mosaic_puzzles,
            'rejected_puzzles': rejected_puzzles[:10]  # Top 10 rejetés
        }
        
        with open("improved_mosaic_results.json", "w") as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 Résultats sauvegardés:")
        print(f"   improved_mosaic_ids.txt - Liste simple")
        print(f"   improved_mosaic_results.json - Données complètes")

def main():
    """Point d'entrée principal"""
    
    detector = ImprovedMosaicDetector()
    mosaic_puzzles, rejected_puzzles = detector.scan_puzzles()
    detector.save_results(mosaic_puzzles, rejected_puzzles)
    
    print(f"\n🎯 COMPARAISON AVEC DÉTECTION PRÉCÉDENTE")
    print("Puzzles probablement exclus avec les nouveaux critères:")
    print("- 6ecd11f4 (faible usage des couleurs)")
    print("- Puzzles avec couleurs dominantes")
    print("- Puzzles avec symétries")

if __name__ == "__main__":
    main()