#!/usr/bin/env python3
"""
Analyse corrigée des données ARC avec regroupements logiques des commandes
"""

import json
import os
import glob
import re
from typing import Dict, List, Any
from analyze_grid.arc_analyzer import ARCAnalyzer  # Correction du nom du module
import pandas as pd
import numpy as np
from collections import Counter, defaultdict

def extract_agi_commands_detailed(agi_content: str) -> Dict[str, Any]:
    """Extrait les commandes avec analyse détaillée des patterns"""
    lines = agi_content.strip().split('\n')
    commands = []
    motif_details = []
    
    in_intelligent_section = False
    
    for line in lines:
        line = line.strip()
        
        if not line:
            continue
            
        if line.startswith('TRANSFERT') or line.startswith('INIT'):
            in_intelligent_section = True
            continue
            
        if line == 'END':
            break
            
        if in_intelligent_section:
            # Analyser les commandes MOTIF en détail
            if line.startswith('MOTIF'):
                commands.append('MOTIF')
                
                # Détecter les transformations dans MOTIF
                has_rotate = 'ROTATE' in line
                has_flip = 'FLIP' in line
                has_multiply = 'MULTIPLY' in line
                has_color = 'COLOR' in line
                has_coordinates = re.search(r'\[\d+,\d+ \d+,\d+\]', line)
                
                motif_type = 'simple'  # Par défaut
                if has_rotate or has_flip or has_multiply:
                    motif_type = 'transformation'
                elif has_color:
                    motif_type = 'color_selection'
                elif has_coordinates:
                    motif_type = 'coordinate_selection'
                
                motif_details.append({
                    'type': motif_type,
                    'has_rotate': has_rotate,
                    'has_flip': has_flip,
                    'has_multiply': has_multiply,
                    'has_color': has_color,
                    'has_coordinates': bool(has_coordinates)
                })
            
            # Autres commandes
            elif line.startswith(('RESIZE', 'EXTRACT')):
                commands.append('RESIZE_EXTRACT')  # Regrouper
            elif line.startswith(('REPLACE', 'REPLACES')):
                commands.append('REPLACE_GROUP')  # Regrouper
            elif line.startswith(('EDIT', 'EDITS')):
                commands.append('EDIT_GROUP')  # Regrouper
            elif line.startswith(('FILL', 'FILLS')):
                commands.append('FILL_GROUP')  # Regrouper
            elif line.startswith('FLOODFILL'):
                commands.append('FLOODFILL')
            elif line.startswith(('ROTATE', 'FLIP')):
                # ROTATE/FLIP standalone (pas dans MOTIF)
                commands.append('GEOMETRIC_TRANSFORM')
    
    # Analyser les patterns MOTIF
    motif_analysis = {
        'count': len(motif_details),
        'types': [m['type'] for m in motif_details],
        'has_transformations': any(m['type'] == 'transformation' for m in motif_details),
        'has_color_selection': any(m['type'] == 'color_selection' for m in motif_details),
        'has_coordinate_selection': any(m['type'] == 'coordinate_selection' for m in motif_details),
        'transformation_count': sum(1 for m in motif_details if m['type'] == 'transformation')
    }
    
    return {
        'commands': commands,
        'motif_analysis': motif_analysis,
        'command_groups': {
            'MOTIF': commands.count('MOTIF'),
            'RESIZE_EXTRACT': commands.count('RESIZE_EXTRACT'),
            'REPLACE_GROUP': commands.count('REPLACE_GROUP'),
            'EDIT_GROUP': commands.count('EDIT_GROUP'),
            'FILL_GROUP': commands.count('FILL_GROUP'),
            'FLOODFILL': commands.count('FLOODFILL'),
            'GEOMETRIC_TRANSFORM': commands.count('GEOMETRIC_TRANSFORM')
        }
    }

def analyze_puzzle_corrected(puzzle_id: str) -> Dict[str, Any]:
    """Analyse corrigée d'un puzzle"""
    json_file = f"../arcdata/training/{puzzle_id}.json"
    agi_file = f"../arcdata/training/{puzzle_id}_TEST0_VALID.agi"
    
    result = {'puzzle_id': puzzle_id}
    
    try:
        # Analyser avec ARCAnalyzer (métriques inchangées)
        if os.path.exists(json_file):
            with open(json_file, 'r') as f:
                puzzle = json.load(f)
            
            analyzer = ARCAnalyzer()
            analysis = analyzer.analyze_puzzle(puzzle)
            
            # Extraire métriques clés (pas toutes pour éviter le bruit)
            key_metrics = {
                'grid_complexity': analysis['complexity']['grid_size_complexity'],
                'color_complexity': analysis['complexity']['color_complexity'],
                'object_complexity': analysis['complexity']['object_complexity'],
                'overall_complexity': analysis['complexity']['overall_complexity'],
                'input_colors_count': len(analysis['colors']['input_colors']),
                'output_colors_count': len(analysis['colors']['output_colors']),
                'scaling_detected': analysis['patterns']['scaling']['detected'],
                'scaling_consistent': analysis['patterns']['scaling'].get('consistent_factor', False),
                'motif_detected': analysis['patterns']['motif']['detected'],
                'motif_confidence': analysis['patterns']['motif']['confidence'],
                'motif_type': analysis['patterns']['motif']['type'],
                'geometric_transforms': len(analysis['transformations']['geometric']),
                'color_transforms': len(analysis['transformations']['color']),
                'structural_transforms': len(analysis['transformations']['structural']),
                'train_examples': analysis['grid_info']['train_examples']
            }
            
            # Calculer ratios et changements de dimensions (CRUCIAL pour RESIZE/EXTRACT)
            if analysis['grid_info']['size_ratios']:
                ratios = analysis['grid_info']['size_ratios']  # Corrected from 'ratios' to 'size_ratios'
                key_metrics['avg_size_ratio_h'] = np.mean([r[0] for r in ratios])
                key_metrics['avg_size_ratio_w'] = np.mean([r[1] for r in ratios])
                key_metrics['size_ratio_consistent'] = len(set(ratios)) == 1
                
                # Métriques spécifiques pour RESIZE/EXTRACT
                key_metrics['has_size_change'] = not all(r[0] == 1.0 and r[1] == 1.0 for r in ratios)
                key_metrics['size_expansion'] = any(r[0] > 1.0 or r[1] > 1.0 for r in ratios)
                key_metrics['size_contraction'] = any(r[0] < 1.0 or r[1] < 1.0 for r in ratios)
                key_metrics['max_size_ratio'] = max(max(r[0], r[1]) for r in ratios)
                key_metrics['min_size_ratio'] = min(min(r[0], r[1]) for r in ratios)
            
            # Changements de dimensions absolus
            if analysis['grid_info']['dimension_changes']:
                dim_changes = analysis['grid_info']['dimension_changes']
                key_metrics['has_dimension_change'] = not all(c[0] == 0 and c[1] == 0 for c in dim_changes)
                key_metrics['avg_height_change'] = np.mean([c[0] for c in dim_changes])
                key_metrics['avg_width_change'] = np.mean([c[1] for c in dim_changes])
                key_metrics['max_dimension_change'] = max(max(abs(c[0]), abs(c[1])) for c in dim_changes)
                key_metrics['dimension_change_consistent'] = analysis['grid_info'].get('consistent_dimension_change', False)
            
            result.update(key_metrics)
        
        # Analyser commandes AGI avec regroupements
        if os.path.exists(agi_file):
            with open(agi_file, 'r', encoding='utf-8') as f:
                agi_content = f.read()
            
            cmd_analysis = extract_agi_commands_detailed(agi_content)
            
            # Ajouter les groupes de commandes
            for group, count in cmd_analysis['command_groups'].items():
                result[f'uses_{group}'] = count > 0
                result[f'count_{group}'] = count
            
            # Ajouter l'analyse MOTIF détaillée
            motif = cmd_analysis['motif_analysis']
            result.update({
                'motif_count': motif['count'],
                'motif_has_transformations': motif['has_transformations'],
                'motif_has_color_selection': motif['has_color_selection'],
                'motif_has_coordinate_selection': motif['has_coordinate_selection'],
                'motif_transformation_count': motif['transformation_count']
            })
            
            result['total_commands'] = str(len(cmd_analysis['commands']))
            result['unique_command_groups'] = str(len([g for g, c in cmd_analysis['command_groups'].items() if c > 0]))
        
        result['success'] = "True"
        
    except Exception as e:
        result['error'] = str(e)
        result['success'] = False
    
    return result

def compute_corrected_correlations(df: pd.DataFrame) -> Dict[str, Any]:
    """Calcule les corrélations avec les groupes corrigés"""
    
    # Métriques clés
    metric_cols = [col for col in df.columns if not col.startswith(('uses_', 'count_', 'motif_', 'puzzle_id', 'success', 'error', 'total_', 'unique_'))]
    
    # Groupes de commandes
    command_groups = ['MOTIF', 'RESIZE_EXTRACT', 'REPLACE_GROUP', 'EDIT_GROUP', 'FILL_GROUP', 'FLOODFILL', 'GEOMETRIC_TRANSFORM']
    
    correlations = {}
    
    for group in command_groups:
        uses_col = f'uses_{group}'
        count_col = f'count_{group}'
        
        if uses_col in df.columns and df[uses_col].sum() > 5:  # Au moins 5 occurrences
            group_correlations = []
            
            for metric_col in metric_cols:
                if df[metric_col].dtype in ['int64', 'float64', 'bool']:
                    try:
                        corr = df[uses_col].corr(df[metric_col])
                        if not pd.isna(corr) and abs(corr) > 0.15:  # Seuil plus élevé
                            group_correlations.append((metric_col, corr))
                    except:
                        continue
            
            group_correlations.sort(key=lambda x: abs(x[1]), reverse=True)
            correlations[group] = group_correlations[:8]  # Top 8
    
    # Analyse spéciale pour MOTIF
    if 'motif_count' in df.columns:
        motif_correlations = []
        
        # Corrélations avec les sous-types de MOTIF
        motif_features = ['motif_has_transformations', 'motif_has_color_selection', 'motif_has_coordinate_selection']
        
        for feature in motif_features:
            if feature in df.columns:
                for metric_col in metric_cols:
                    if df[metric_col].dtype in ['int64', 'float64', 'bool']:
                        try:
                            corr = df[feature].corr(df[metric_col])
                            if not pd.isna(corr) and abs(corr) > 0.15:
                                motif_correlations.append((f"{feature} ↔ {metric_col}", corr))
                        except:
                            continue
        
        motif_correlations.sort(key=lambda x: abs(x[1]), reverse=True)
        correlations['MOTIF_SUBTYPES'] = motif_correlations[:10]
    
    return correlations

def main():
    """Analyse corrigée des données ARC"""
    print("🔍 ANALYSE CORRIGÉE DES DONNÉES ARC")
    print("Regroupements logiques des commandes")
    print("=" * 50)
    
    # Collecter tous les puzzles
    all_puzzles = [os.path.basename(f).replace('.json', '') for f in glob.glob("../arcdata/training/*.json")]
    print(f"📋 {len(all_puzzles)} puzzles à analyser")
    
    # Analyser avec regroupements corrigés
    print("\n🧪 Collecte des données corrigées...")
    all_data = []
    
    for i, puzzle_id in enumerate(all_puzzles):
        if (i + 1) % 50 == 0:
            print(f"  Progress: {i+1}/{len(all_puzzles)} ({(i+1)/len(all_puzzles)*100:.1f}%)")
        
        data = analyze_puzzle_corrected(puzzle_id)
        all_data.append(data)
    
    # Créer DataFrame
    df = pd.DataFrame(all_data)
    successful_data = df[df['success'] == True].copy()
    print(f"Données valides: {len(successful_data)}/{len(df)}")
    
    # Statistiques des groupes de commandes
    print(f"\n📈 Statistiques des groupes de commandes:")
    command_groups = ['MOTIF', 'RESIZE_EXTRACT', 'REPLACE_GROUP', 'EDIT_GROUP', 'FILL_GROUP', 'FLOODFILL', 'GEOMETRIC_TRANSFORM']
    
    for group in command_groups:
        uses_col = f'uses_{group}'
        if uses_col in successful_data.columns:
            count = successful_data[uses_col].sum()
            percentage = count / len(successful_data) * 100
            print(f"  {group}: {count} puzzles ({percentage:.1f}%)")
    
    # Statistiques MOTIF détaillées
    if 'motif_count' in successful_data.columns:
        motif_data = successful_data[successful_data['uses_MOTIF'] == True]
        print(f"\n🎯 Analyse détaillée MOTIF ({len(motif_data)} puzzles):")
        
        if len(motif_data) > 0:
            trans_count = motif_data['motif_has_transformations'].sum()
            color_count = motif_data['motif_has_color_selection'].sum()
            coord_count = motif_data['motif_has_coordinate_selection'].sum()
            
            print(f"  - Avec transformations: {trans_count} ({trans_count/len(motif_data)*100:.1f}%)")
            print(f"  - Avec sélection couleur: {color_count} ({color_count/len(motif_data)*100:.1f}%)")
            print(f"  - Avec sélection coordonnées: {coord_count} ({coord_count/len(motif_data)*100:.1f}%)")
    
    # Calculer corrélations corrigées
    print(f"\n🔗 Calcul des corrélations corrigées...")
    correlations = compute_corrected_correlations(successful_data)
    
    # Afficher les corrélations
    print(f"\n🎯 CORRÉLATIONS DÉCOUVERTES (CORRIGÉES):")
    print("=" * 50)
    
    for group, corr_list in correlations.items():
        if corr_list:
            if group in ['MOTIF', 'RESIZE_EXTRACT', 'REPLACE_GROUP', 'EDIT_GROUP', 'FILL_GROUP', 'FLOODFILL']:
                uses_col = f'uses_{group}'
                count = successful_data[uses_col].sum() if uses_col in successful_data.columns else 0
                print(f"\n🔧 {group} ({count} puzzles):")
            else:
                print(f"\n🔧 {group}:")
            
            for item, corr_value in corr_list[:6]:  # Top 6
                direction = "↗️" if corr_value > 0 else "↘️"
                print(f"  {direction} {item}: {corr_value:.3f}")
    
    # Sauvegarder résultats
    results = {
        'analysis_date': pd.Timestamp.now().isoformat(),
        'total_puzzles': len(all_puzzles),
        'successful_analyses': len(successful_data),
        'command_group_statistics': {
            group: {
                'count': int(successful_data[f'uses_{group}'].sum()) if f'uses_{group}' in successful_data.columns else 0,
                'percentage': float(successful_data[f'uses_{group}'].sum() / len(successful_data) * 100) if f'uses_{group}' in successful_data.columns else 0
            } for group in command_groups
        },
        'motif_detailed_stats': {
            'total_motif_puzzles': int(successful_data['uses_MOTIF'].sum()) if 'uses_MOTIF' in successful_data.columns else 0,
            'with_transformations': int(successful_data[successful_data['uses_MOTIF'] == True]['motif_has_transformations'].sum()) if 'motif_has_transformations' in successful_data.columns else 0,
            'with_color_selection': int(successful_data[successful_data['uses_MOTIF'] == True]['motif_has_color_selection'].sum()) if 'motif_has_color_selection' in successful_data.columns else 0,
            'with_coordinate_selection': int(successful_data[successful_data['uses_MOTIF'] == True]['motif_has_coordinate_selection'].sum()) if 'motif_has_coordinate_selection' in successful_data.columns else 0
        },
        'correlations': {k: [(item, float(corr)) for item, corr in v] for k, v in correlations.items()}
    }
    
    with open('corrected_arc_analysis.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    successful_data.to_csv('corrected_arc_data_matrix.csv', index=False)
    
    print(f"\n💾 Résultats sauvegardés:")
    print(f"  - corrected_arc_analysis.json: Analyse corrigée")
    print(f"  - corrected_arc_data_matrix.csv: Matrice de données")
    
    print(f"\n🎯 INSIGHTS CORRIGÉS:")
    print(f"  - Regroupements logiques appliqués")
    print(f"  - MOTIF analysé en sous-types")
    print(f"  - Corrélations plus précises")

if __name__ == "__main__":
    main()