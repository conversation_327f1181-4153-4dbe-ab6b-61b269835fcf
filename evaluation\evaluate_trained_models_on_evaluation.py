#!/usr/bin/env python3
"""
Comprehensive evaluation script for trained ARC models on evaluation dataset.
Tests multiple trained models and generates AGI scenario files similar to training folder structure.
"""

import json
import numpy as np
from pathlib import Path
import time
from typing import Dict, List, Tuple
import argparse
from tqdm import tqdm
import shutil
import torch
import traceback
from datetime import datetime

import sys
sys.path.append('..')
from config import Config
from src.tokenizer import GrammarTokenizer
from models.hrm_model import GridToProgramHRM
from models.inference import generate_program, execute_agi_program
from src.command_executor import CommandExecutor

class TrainedModelEvaluator:
    """Evaluator for testing trained ARC models on evaluation dataset."""
    
    def __init__(self, model_path: str, output_dir: str = "evaluation_results"):
        self.model_path = model_path
        self.model_name = Path(model_path).stem
        self.config = Config()
        self.device = self.config.DEVICE
        
        # Initialize components
        self.tokenizer = GrammarTokenizer()
        self.command_executor = CommandExecutor()
        
        # Output directories
        self.output_dir = Path(output_dir)
        self.agi_dir = self.output_dir / "agi_files"
        self.metadata_dir = self.output_dir / "metadata"
        self.analysis_dir = self.output_dir / "analysis"
        
        # Create directories
        for dir_path in [self.output_dir, self.agi_dir, self.metadata_dir, self.analysis_dir]:
            dir_path.mkdir(exist_ok=True, parents=True)
        
        # Load model
        self.model = self._load_model()
        
        # Results storage
        self.results = {
            "model_name": self.model_name,
            "model_path": str(model_path),
            "evaluation_timestamp": datetime.now().isoformat(),
            "total_puzzles": 0,
            "successful_generations": 0,
            "valid_agi_files": 0,
            "execution_successful": 0,
            "average_time": 0.0,
            "puzzle_results": [],
            "performance_metrics": {},
            "error_analysis": {}
        }
    
    def _load_model(self) -> GridToProgramHRM:
        """Load the trained HRM model."""
        print(f"Loading model from {self.model_path}...")
        
        # Initialize model
        model = GridToProgramHRM(
            model_dim=self.config.MODEL_DIM,
            n_heads=self.config.N_HEADS,
            grammar_vocab_size=len(self.tokenizer.vocab),
            N_cycles=self.config.N_CYCLES,
            T_steps=self.config.T_STEPS
        )
        
        # Load weights
        if Path(self.model_path).exists():
            try:
                state_dict = torch.load(self.model_path, map_location=self.device)
                
                # Handle different checkpoint formats
                if 'model_state_dict' in state_dict:
                    model.load_state_dict(state_dict['model_state_dict'])
                elif 'state_dict' in state_dict:
                    model.load_state_dict(state_dict['state_dict'])
                else:
                    model.load_state_dict(state_dict)
                    
                print(f"✅ Model {self.model_name} loaded successfully!")
            except Exception as e:
                print(f"⚠️ Error loading model weights: {e}")
                print("Using untrained model for comparison...")
        else:
            print(f"⚠️ Model file {self.model_path} not found. Using untrained model.")
        
        model.to(self.device)
        model.eval()
        return model

    def _simple_validate_program(self, program: str) -> Dict:
        """Simple program validation without complex dependencies."""
        if not program or not program.strip():
            return {"valid": False, "errors": ["Empty program"]}

        # Basic checks
        lines = program.strip().split('\n')
        errors = []

        # Check for basic command structure
        valid_commands = ['INIT', 'FILL', 'COPY', 'MOVE', 'ROTATE', 'REFLECT', 'SCALE', 'CROP']

        for i, line in enumerate(lines):
            line = line.strip()
            if not line or line.startswith('#'):
                continue

            # Check if line starts with a valid command
            if not any(line.startswith(cmd) for cmd in valid_commands):
                errors.append(f"Line {i+1}: Unknown command '{line.split()[0] if line.split() else line}'")

        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "line_count": len([l for l in lines if l.strip() and not l.strip().startswith('#')])
        }
    
    def evaluate_on_evaluation_dataset(self, dataset_path: str = "../arcdata/evaluation", 
                                     max_puzzles: int = None) -> Dict:
        """
        Evaluate model on the ARC evaluation dataset.
        
        Args:
            dataset_path: Path to evaluation dataset
            max_puzzles: Maximum number of puzzles to evaluate
        """
        print(f"🎯 EVALUATING {self.model_name.upper()} ON EVALUATION DATASET")
        print("=" * 70)
        
        dataset_path = Path(dataset_path)
        
        # Get all JSON puzzle files
        puzzle_files = []
        for file in dataset_path.glob("*.json"):
            if len(file.stem) == 8 and all(c in '0123456789abcdef' for c in file.stem.lower()):
                puzzle_files.append(file)
        
        if max_puzzles:
            puzzle_files = puzzle_files[:max_puzzles]
        
        print(f"📁 Dataset: {dataset_path}")
        print(f"🧩 Puzzles to evaluate: {len(puzzle_files)}")
        print(f"📂 Output directory: {self.output_dir}")
        print(f"🤖 Model: {self.model_name}")
        print()
        
        # Process each puzzle
        total_time = 0
        for puzzle_file in tqdm(puzzle_files, desc=f"Evaluating {self.model_name}"):
            result = self._process_puzzle(puzzle_file)
            self.results["puzzle_results"].append(result)
            total_time += result.get('execution_time', 0)
        
        # Compute final statistics
        self._compute_statistics(total_time)
        
        # Generate evaluation report
        self._generate_evaluation_report()
        
        return self.results
    
    def _process_puzzle(self, puzzle_file: Path) -> Dict:
        """Process a single puzzle and generate AGI scenario."""
        puzzle_id = puzzle_file.stem
        
        try:
            # Load puzzle data
            with open(puzzle_file, 'r') as f:
                puzzle_data = json.load(f)
            
            # Validate puzzle structure
            if not puzzle_data.get('train') or not puzzle_data.get('test'):
                return {
                    "puzzle_id": puzzle_id,
                    "success": False,
                    "error": "Invalid puzzle structure",
                    "execution_time": 0
                }
            
            # Process test case (evaluation dataset typically has one test case)
            test_case = puzzle_data['test'][0]
            input_grid = test_case['input']
            expected_output = test_case.get('output')  # May not be available in evaluation
            
            # Generate program
            start_time = time.time()
            generated_program = generate_program(
                self.model, 
                input_grid, 
                self.tokenizer, 
                device=self.device,
                max_length=200,
                temperature=0.7
            )
            generation_time = time.time() - start_time
            
            # Update counters
            self.results["total_puzzles"] += 1
            
            if generated_program and len(generated_program.strip()) > 0:
                self.results["successful_generations"] += 1
                
                # Simple program validation
                validation_result = self._simple_validate_program(generated_program)
                
                # Execute program if valid
                execution_result = None
                if validation_result.get('valid', False):
                    try:
                        execution_result = execute_agi_program(generated_program, input_grid)
                        if execution_result is not None:
                            self.results["execution_successful"] += 1
                    except Exception as e:
                        execution_result = None
                
                # Generate AGI file
                self._generate_agi_file(puzzle_id, puzzle_data, generated_program, validation_result)
                
                # Generate metadata
                self._generate_metadata_file(puzzle_id, {
                    "generation_time": generation_time,
                    "program_length": len(generated_program),
                    "validation_result": validation_result,
                    "execution_successful": execution_result is not None
                })
                
                self.results["valid_agi_files"] += 1
            
            return {
                "puzzle_id": puzzle_id,
                "success": generated_program is not None and len(generated_program.strip()) > 0,
                "execution_time": generation_time,
                "program_length": len(generated_program) if generated_program else 0,
                "validation_passed": validation_result.get('valid', False) if generated_program else False,
                "execution_successful": execution_result is not None if generated_program else False,
                "generated_program": generated_program
            }
            
        except Exception as e:
            self.results["total_puzzles"] += 1
            return {
                "puzzle_id": puzzle_id,
                "success": False,
                "error": str(e),
                "execution_time": 0
            }
    
    def _generate_agi_file(self, puzzle_id: str, puzzle_data: Dict, program: str, validation: Dict):
        """Generate AGI scenario file similar to training folder structure."""
        agi_content = []
        
        # Header
        agi_content.append(f"# ARC Puzzle {puzzle_id}")
        agi_content.append(f"# Generated by trained model: {self.model_name}")
        agi_content.append(f"# Timestamp: {datetime.now().isoformat()}")
        agi_content.append("")
        
        # Puzzle information
        test_input = puzzle_data['test'][0]['input']
        h, w = len(test_input), len(test_input[0])
        agi_content.append(f"# Input dimensions: {w}x{h}")
        agi_content.append(f"# Training examples: {len(puzzle_data['train'])}")
        agi_content.append("")
        
        # Validation status
        agi_content.append("# Validation Status:")
        agi_content.append(f"# Valid syntax: {validation.get('valid', False)}")
        if validation.get('errors'):
            agi_content.append(f"# Validation errors: {validation['errors']}")
        agi_content.append("")
        
        # Generated program
        agi_content.append("# Generated Program:")
        if program and program.strip():
            for line in program.strip().split('\n'):
                agi_content.append(line)
        else:
            agi_content.append("# No valid program generated")
        
        # Save AGI file
        agi_file = self.agi_dir / f"{puzzle_id}.agi"
        with open(agi_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(agi_content))
    
    def _generate_metadata_file(self, puzzle_id: str, metadata: Dict):
        """Generate metadata file for the puzzle."""
        metadata_content = {
            "puzzle_id": puzzle_id,
            "model_name": self.model_name,
            "model_path": str(self.model_path),
            "generation_timestamp": datetime.now().isoformat(),
            **metadata
        }
        
        metadata_file = self.metadata_dir / f"{puzzle_id}_metadata.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata_content, f, indent=2)

    def _compute_statistics(self, total_time: float):
        """Compute evaluation statistics."""
        if self.results["total_puzzles"] > 0:
            self.results["success_rate"] = self.results["successful_generations"] / self.results["total_puzzles"]
            self.results["valid_agi_rate"] = self.results["valid_agi_files"] / self.results["total_puzzles"]
            self.results["execution_rate"] = self.results["execution_successful"] / self.results["total_puzzles"]
            self.results["average_time"] = total_time / self.results["total_puzzles"]

        # Performance metrics
        self.results["performance_metrics"] = {
            "total_puzzles_processed": self.results["total_puzzles"],
            "program_generation_success_rate": self.results["success_rate"],
            "valid_agi_file_rate": self.results["valid_agi_rate"],
            "execution_success_rate": self.results["execution_rate"],
            "average_generation_time_seconds": self.results["average_time"],
            "total_evaluation_time_seconds": total_time
        }

    def _generate_evaluation_report(self):
        """Generate comprehensive evaluation report."""
        report_lines = []

        report_lines.append(f"# EVALUATION REPORT - {self.model_name.upper()}")
        report_lines.append("=" * 70)
        report_lines.append("")
        report_lines.append(f"**Model:** {self.model_name}")
        report_lines.append(f"**Model Path:** {self.model_path}")
        report_lines.append(f"**Evaluation Date:** {self.results['evaluation_timestamp']}")
        report_lines.append("")

        # Performance Summary
        report_lines.append("## PERFORMANCE SUMMARY")
        report_lines.append(f"- **Total Puzzles Processed:** {self.results['total_puzzles']}")
        report_lines.append(f"- **Successful Program Generations:** {self.results['successful_generations']}")
        report_lines.append(f"- **Valid AGI Files Generated:** {self.results['valid_agi_files']}")
        report_lines.append(f"- **Successful Executions:** {self.results['execution_successful']}")
        report_lines.append("")
        report_lines.append(f"- **Program Generation Success Rate:** {self.results.get('success_rate', 0):.1%}")
        report_lines.append(f"- **Valid AGI File Rate:** {self.results.get('valid_agi_rate', 0):.1%}")
        report_lines.append(f"- **Execution Success Rate:** {self.results.get('execution_rate', 0):.1%}")
        report_lines.append(f"- **Average Generation Time:** {self.results['average_time']:.3f}s")
        report_lines.append("")

        # Files Generated
        agi_files = list(self.agi_dir.glob("*.agi"))
        metadata_files = list(self.metadata_dir.glob("*_metadata.json"))

        report_lines.append("## FILES GENERATED")
        report_lines.append(f"- **AGI Scenario Files:** {len(agi_files)}")
        report_lines.append(f"- **Metadata Files:** {len(metadata_files)}")
        report_lines.append(f"- **Output Directory:** {self.output_dir}")
        report_lines.append("")

        # Error Analysis
        errors = [r for r in self.results["puzzle_results"] if not r.get("success", False)]
        if errors:
            report_lines.append("## ERROR ANALYSIS")
            report_lines.append(f"- **Failed Puzzles:** {len(errors)}")

            # Group errors by type
            error_types = {}
            for error in errors:
                error_msg = error.get("error", "Unknown error")
                error_types[error_msg] = error_types.get(error_msg, 0) + 1

            for error_type, count in sorted(error_types.items(), key=lambda x: x[1], reverse=True):
                report_lines.append(f"  - {error_type}: {count} cases")
            report_lines.append("")

        # Model Comparison Context
        report_lines.append("## MODEL CONTEXT")
        report_lines.append("This evaluation tests the trained model on the ARC evaluation dataset.")
        report_lines.append("Generated AGI scenario files follow the same structure as the training folder.")
        report_lines.append("Each puzzle generates:")
        report_lines.append("- An .agi file with the generated program")
        report_lines.append("- A metadata JSON file with generation details")
        report_lines.append("")

        # Save report
        report_file = self.output_dir / f"evaluation_report_{self.model_name}.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))

        print(f"\n📊 Evaluation report saved: {report_file}")

    def save_results(self, filename: str = None):
        """Save detailed results to JSON file."""
        if filename is None:
            filename = f"evaluation_results_{self.model_name}.json"

        results_file = self.output_dir / filename
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2)

        print(f"📄 Detailed results saved: {results_file}")


class MultiModelEvaluator:
    """Evaluator for comparing multiple trained models."""

    def __init__(self, output_dir: str = "multi_model_evaluation"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True, parents=True)
        self.model_results = {}

    def evaluate_multiple_models(self, model_paths: List[str], dataset_path: str = "../arcdata/evaluation",
                                max_puzzles: int = None) -> Dict:
        """Evaluate multiple models and compare results."""
        print("🚀 MULTI-MODEL EVALUATION ON ARC EVALUATION DATASET")
        print("=" * 70)

        for model_path in model_paths:
            model_name = Path(model_path).stem
            print(f"\n🤖 Evaluating model: {model_name}")

            # Create model-specific output directory
            model_output_dir = self.output_dir / f"results_{model_name}"

            # Evaluate model
            evaluator = TrainedModelEvaluator(model_path, str(model_output_dir))
            results = evaluator.evaluate_on_evaluation_dataset(dataset_path, max_puzzles)

            # Save results
            evaluator.save_results()

            # Store for comparison
            self.model_results[model_name] = results

        # Generate comparison report
        self._generate_comparison_report()

        return self.model_results

    def _generate_comparison_report(self):
        """Generate comparison report across all models."""
        report_lines = []

        report_lines.append("# MULTI-MODEL COMPARISON REPORT")
        report_lines.append("=" * 70)
        report_lines.append("")
        report_lines.append(f"**Evaluation Date:** {datetime.now().isoformat()}")
        report_lines.append(f"**Models Evaluated:** {len(self.model_results)}")
        report_lines.append("")

        # Performance comparison table
        report_lines.append("## PERFORMANCE COMPARISON")
        report_lines.append("")
        report_lines.append("| Model | Success Rate | Valid AGI Rate | Execution Rate | Avg Time (s) |")
        report_lines.append("|-------|--------------|----------------|----------------|--------------|")

        for model_name, results in self.model_results.items():
            success_rate = results.get('success_rate', 0) * 100
            valid_rate = results.get('valid_agi_rate', 0) * 100
            exec_rate = results.get('execution_rate', 0) * 100
            avg_time = results.get('average_time', 0)

            report_lines.append(f"| {model_name} | {success_rate:.1f}% | {valid_rate:.1f}% | {exec_rate:.1f}% | {avg_time:.3f} |")

        report_lines.append("")

        # Best performing model
        if self.model_results:
            best_model = max(self.model_results.items(),
                           key=lambda x: x[1].get('success_rate', 0))
            report_lines.append("## BEST PERFORMING MODEL")
            report_lines.append(f"**{best_model[0]}** achieved the highest success rate of {best_model[1].get('success_rate', 0):.1%}")
            report_lines.append("")

        # Summary statistics
        report_lines.append("## SUMMARY STATISTICS")
        total_puzzles = sum(r.get('total_puzzles', 0) for r in self.model_results.values())
        total_successful = sum(r.get('successful_generations', 0) for r in self.model_results.values())
        total_agi_files = sum(r.get('valid_agi_files', 0) for r in self.model_results.values())

        report_lines.append(f"- **Total Puzzles Processed:** {total_puzzles}")
        report_lines.append(f"- **Total Successful Generations:** {total_successful}")
        report_lines.append(f"- **Total AGI Files Generated:** {total_agi_files}")
        report_lines.append("")

        # Save comparison report
        report_file = self.output_dir / "multi_model_comparison_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))

        print(f"\n📊 Multi-model comparison report saved: {report_file}")


def main():
    """Main function for model evaluation."""
    parser = argparse.ArgumentParser(description="Evaluate trained ARC models on evaluation dataset")
    parser.add_argument("--model", type=str, help="Path to single model to evaluate")
    parser.add_argument("--models", nargs='+', help="Paths to multiple models to evaluate")
    parser.add_argument("--dataset", default="../arcdata/evaluation",
                       help="Path to evaluation dataset")
    parser.add_argument("--max-puzzles", type=int, default=50,
                       help="Maximum number of puzzles to evaluate")
    parser.add_argument("--output", default="evaluation_results",
                       help="Output directory for results")

    args = parser.parse_args()

    # Default models if none specified
    if not args.model and not args.models:
        default_models = [
            "../best_hrm_improved.pth",
            "../hrm_improved_final.pth",
            "../hrm_arc_solver_improved.pth",
            "../hrm_improved_epoch_50.pth"
        ]
        # Filter to existing models
        args.models = [m for m in default_models if Path(m).exists()]

        if not args.models:
            print("❌ No trained models found. Please specify model paths.")
            return

    if args.model:
        # Single model evaluation
        print(f"🎯 Single Model Evaluation: {args.model}")
        evaluator = TrainedModelEvaluator(args.model, args.output)
        results = evaluator.evaluate_on_evaluation_dataset(args.dataset, args.max_puzzles)
        evaluator.save_results()

        print(f"\n✅ EVALUATION COMPLETED")
        print(f"Success Rate: {results.get('success_rate', 0):.1%}")
        print(f"Valid AGI Files: {results['valid_agi_files']}")
        print(f"Output Directory: {args.output}")

    elif args.models:
        # Multi-model evaluation
        print(f"🎯 Multi-Model Evaluation: {len(args.models)} models")
        multi_evaluator = MultiModelEvaluator(args.output)
        results = multi_evaluator.evaluate_multiple_models(args.models, args.dataset, args.max_puzzles)

        print(f"\n✅ MULTI-MODEL EVALUATION COMPLETED")
        print(f"Models Evaluated: {len(results)}")
        print(f"Output Directory: {args.output}")


if __name__ == "__main__":
    main()
