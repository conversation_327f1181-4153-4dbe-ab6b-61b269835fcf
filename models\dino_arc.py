"""
DINO-ARC : Modèle d'analyse multi-exemples pour puzzles ARC.

Adapte GridDINO pour analyser plusieurs paires (input, output) simultanément
et détecter les patterns communs pour la catégorisation automatique.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np
from typing import Dict, List, Tuple, Optional


class ValueEmbedding(nn.Module):
    """Embedding intelligent des valeurs de grille avec contexte spatial."""
    def __init__(self, vocab_size=10, embed_dim=128, context_size=3):
        super().__init__()
        self.value_embed = nn.Embedding(vocab_size, embed_dim)
        self.context_conv = nn.Conv2d(embed_dim, embed_dim, 
                                     kernel_size=context_size, 
                                     padding=context_size//2)
        
    def forward(self, grid):
        # grid: [batch, height, width]
        val_emb = self.value_embed(grid)  # [batch, H, W, D]
        
        # Contexte spatial
        val_emb_perm = val_emb.permute(0, 3, 1, 2)  # [batch, D, H, W]
        context_emb = self.context_conv(val_emb_perm)
        context_emb = context_emb.permute(0, 2, 3, 1)  # [batch, H, W, D]
        
        return val_emb + context_emb


class PositionalEncoding2D(nn.Module):
    """Encodage positionnel 2D pour grilles."""
    def __init__(self, dim, max_size=30):
        super().__init__()
        self.row_enc = nn.Embedding(max_size, dim//2)
        self.col_enc = nn.Embedding(max_size, dim//2)
        
    def forward(self, x):
        # x: [batch, H, W, D]
        batch, height, width, _ = x.shape
        
        # Générer les positions
        rows = torch.arange(height, device=x.device).view(height, 1)
        cols = torch.arange(width, device=x.device).view(1, width)
        
        # Embeddings de position
        row_emb = self.row_enc(rows)  # [height, 1, dim//2]
        col_emb = self.col_enc(cols)  # [1, width, dim//2]
        
        # Expansion pour correspondre aux dimensions de la grille
        row_emb = row_emb.expand(height, width, -1)  # [height, width, dim//2]
        col_emb = col_emb.expand(height, width, -1)  # [height, width, dim//2]
        
        # Combinaison et expansion pour le batch
        pos_emb = torch.cat([row_emb, col_emb], dim=-1)  # [height, width, dim]
        pos_emb = pos_emb.unsqueeze(0).expand(batch, -1, -1, -1)  # [batch, height, width, dim]
        
        return x + pos_emb


class GeometricAttention(nn.Module):
    """Attention spatiale avec contraintes géométriques."""
    def __init__(self, dim, num_heads=8, grid_size=30):
        super().__init__()
        self.num_heads = num_heads
        self.head_dim = dim // num_heads
        self.scale = self.head_dim ** -0.5
        
        # Projections pour Q, K, V
        self.to_qkv = nn.Linear(dim, dim * 3, bias=False)
        
        # Embedding des distances géométriques
        max_dist = 2 * (grid_size - 1)
        self.dist_embed = nn.Embedding(max_dist + 1, num_heads)
        
        # Output projection
        self.to_out = nn.Linear(dim, dim)
        
    def forward(self, x):
        batch, height, width, _ = x.shape
        
        # Générer la matrice de distances Manhattan
        row_indices = torch.arange(height, device=x.device)
        col_indices = torch.arange(width, device=x.device)
        
        row_diff = torch.abs(row_indices.unsqueeze(1) - row_indices.unsqueeze(0))
        col_diff = torch.abs(col_indices.unsqueeze(1) - col_indices.unsqueeze(0))
        
        # Distance Manhattan pour chaque paire de positions
        distances = row_diff.unsqueeze(2).unsqueeze(3) + col_diff.unsqueeze(0).unsqueeze(1)
        distances = distances.view(height * width, height * width)
        
        # Embedding des distances
        dist_emb = self.dist_embed(distances)  # [H*W, H*W, num_heads]
        dist_emb = dist_emb.permute(2, 0, 1)  # [num_heads, H*W, H*W]
        
        # Reshape pour attention
        x_flat = x.view(batch, height * width, -1)  # [batch, H*W, D]
        
        # Projection Q, K, V
        qkv = self.to_qkv(x_flat).chunk(3, dim=-1)
        q, k, v = map(lambda t: t.view(batch, height * width, self.num_heads, self.head_dim).transpose(1, 2), qkv)
        
        # Calcul des scores d'attention avec biais géométrique
        attn = torch.matmul(q, k.transpose(-2, -1)) * self.scale
        attn = attn + dist_emb.unsqueeze(0)  # [batch, num_heads, H*W, H*W]
        
        # Softmax et agrégation
        attn = attn.softmax(dim=-1)
        out = torch.matmul(attn, v)  # [batch, num_heads, H*W, head_dim]
        
        # Reshape et projection finale
        out = out.transpose(1, 2).contiguous().view(batch, height * width, -1)
        out = self.to_out(out)
        
        return out.view(batch, height, width, -1)


class TransformerBlock(nn.Module):
    """Bloc Transformer spatial complet."""
    def __init__(self, dim, num_heads, grid_size=30):
        super().__init__()
        self.norm1 = nn.LayerNorm(dim)
        self.attn = GeometricAttention(dim, num_heads, grid_size)
        self.norm2 = nn.LayerNorm(dim)
        self.mlp = nn.Sequential(
            nn.Linear(dim, dim * 4),
            nn.GELU(),
            nn.Linear(dim * 4, dim)
        )
        
    def forward(self, x):
        # Attention résiduelle
        x = x + self.attn(self.norm1(x))
        
        # MLP résiduel
        x = x + self.mlp(self.norm2(x))
        return x


class PatternDetectionHead(nn.Module):
    """Tête de détection de motifs spécifiques pour ARC."""
    def __init__(self, dim, pattern_type):
        super().__init__()
        self.pattern_type = pattern_type
        
        # Différents kernels selon le type de pattern
        if pattern_type == 'horizontal_line':
            self.conv = nn.Conv2d(dim, 1, kernel_size=(1, 5), padding=(0, 2))
        elif pattern_type == 'vertical_line':
            self.conv = nn.Conv2d(dim, 1, kernel_size=(5, 1), padding=(2, 0))
        elif pattern_type == 'rectangle':
            self.conv = nn.Conv2d(dim, 1, kernel_size=3, padding=1)
        elif pattern_type == 'symmetry':
            self.conv = nn.Conv2d(dim, 1, kernel_size=5, padding=2)
        elif pattern_type == 'repetition':
            self.conv = nn.Conv2d(dim, 1, kernel_size=7, padding=3)
        elif pattern_type == 'color_change':
            self.conv = nn.Conv2d(dim, 1, kernel_size=3, padding=1)
        else:
            # Pattern générique
            self.conv = nn.Conv2d(dim, 1, kernel_size=3, padding=1)
        
    def forward(self, x):
        # x: [batch, H, W, D]
        x = x.permute(0, 3, 1, 2)  # [batch, D, H, W]
        return self.conv(x).squeeze(1)  # [batch, H, W]


class MultiExampleEncoder(nn.Module):
    """
    Encodeur pour traiter plusieurs paires (input, output) simultanément.
    
    Analyse les patterns communs entre tous les exemples d'entraînement
    d'un puzzle pour détecter la règle sous-jacente.
    """
    def __init__(self, embed_dim=128):
        super().__init__()
        self.pair_attention = nn.MultiheadAttention(embed_dim, num_heads=8, batch_first=True)
        self.norm = nn.LayerNorm(embed_dim)
        
    def forward(self, input_features, output_features):
        """
        Args:
            input_features: [batch, num_examples, H, W, D]
            output_features: [batch, num_examples, H, W, D]
        
        Returns:
            pattern_features: [batch, D] - Features des patterns détectés
        """
        batch, num_examples, H, W, D = input_features.shape
        
        # Combiner input et output pour chaque exemple
        # [batch, num_examples, 2*H*W, D]
        combined_features = torch.cat([
            input_features.view(batch, num_examples, H*W, D),
            output_features.view(batch, num_examples, H*W, D)
        ], dim=2)
        
        # Attention entre les exemples pour détecter les patterns communs
        # Reshape pour attention: [batch*num_examples, 2*H*W, D]
        combined_flat = combined_features.view(batch * num_examples, 2*H*W, D)
        
        # Self-attention pour capturer les relations input-output
        attended, _ = self.pair_attention(combined_flat, combined_flat, combined_flat)
        attended = self.norm(attended + combined_flat)
        
        # Pooling global pour obtenir une représentation par exemple
        example_features = attended.mean(dim=1)  # [batch*num_examples, D]
        example_features = example_features.view(batch, num_examples, D)
        
        # Pooling entre exemples pour obtenir les patterns communs
        pattern_features = example_features.mean(dim=1)  # [batch, D]
        
        return pattern_features


class ARCCategorizer(nn.Module):
    """
    Module de catégorisation automatique des puzzles ARC.
    
    Prédit la catégorie d'un puzzle basé sur les patterns détectés
    dans les exemples d'entraînement.
    """
    def __init__(self, feature_dim=128, num_categories=20):
        super().__init__()
        
        # Catégories principales des puzzles ARC
        self.categories = [
            'color_change', 'shape_completion', 'pattern_repetition',
            'symmetry', 'rotation', 'scaling', 'translation',
            'object_counting', 'line_drawing', 'rectangle_filling',
            'mosaic_pattern', 'grid_transformation', 'color_mapping',
            'shape_extraction', 'pattern_overlay', 'geometric_rule',
            'conditional_fill', 'boundary_detection', 'template_match',
            'unknown'
        ]
        
        self.classifier = nn.Sequential(
            nn.Linear(feature_dim, feature_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(feature_dim * 2, feature_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(feature_dim, len(self.categories))
        )
        
    def forward(self, pattern_features):
        """
        Args:
            pattern_features: [batch, feature_dim]
        
        Returns:
            Dict avec:
            - category_logits: [batch, num_categories]
            - category_probs: [batch, num_categories]
            - predicted_category: [batch] - indices des catégories prédites
        """
        logits = self.classifier(pattern_features)
        probs = F.softmax(logits, dim=-1)
        predicted = torch.argmax(logits, dim=-1)
        
        return {
            'category_logits': logits,
            'category_probs': probs,
            'predicted_category': predicted
        }


class DinoARC(nn.Module):
    """
    Modèle principal DINO-ARC pour l'analyse multi-exemples.
    
    Analyse tous les exemples train d'un puzzle ARC simultanément
    pour détecter les patterns communs et catégoriser automatiquement.
    """
    def __init__(self, 
                 grid_size=30, 
                 vocab_size=10, 
                 embed_dim=128, 
                 num_layers=6, 
                 num_heads=8,
                 num_categories=20):
        super().__init__()
        self.grid_size = grid_size
        self.embed_dim = embed_dim
        
        # Embeddings de base
        self.value_embed = ValueEmbedding(vocab_size, embed_dim)
        self.pos_enc = PositionalEncoding2D(embed_dim, grid_size)
        
        # Transformer spatial pour chaque grille
        self.blocks = nn.ModuleList([
            TransformerBlock(embed_dim, num_heads, grid_size)
            for _ in range(num_layers)
        ])
        
        # Têtes de détection de patterns ARC
        pattern_types = ['horizontal_line', 'vertical_line', 'rectangle', 
                        'symmetry', 'repetition', 'color_change']
        self.pattern_heads = nn.ModuleDict({
            ptype: PatternDetectionHead(embed_dim, ptype)
            for ptype in pattern_types
        })
        
        # Encodeur multi-exemples
        self.multi_example_encoder = MultiExampleEncoder(embed_dim)
        
        # Catégorisateur
        self.categorizer = ARCCategorizer(embed_dim, num_categories)
        
    def encode_grid(self, grid):
        """
        Encode une grille individuelle.
        
        Args:
            grid: [batch, H, W]
        
        Returns:
            features: [batch, H, W, D]
            pattern_maps: Dict[str, [batch, H, W]]
        """
        # Embedding de base
        x = self.value_embed(grid)
        x = self.pos_enc(x)
        
        # Blocs de transformation
        for block in self.blocks:
            x = block(x)
        
        # Détection de motifs
        pattern_maps = {}
        for ptype, head in self.pattern_heads.items():
            pattern_maps[ptype] = head(x)
        
        return x, pattern_maps
    
    def forward(self, train_pairs):
        """
        Analyse complète d'un puzzle avec plusieurs exemples train.
        
        Args:
            train_pairs: List[Tuple[torch.Tensor, torch.Tensor]]
                        Liste de paires (input_grid, output_grid)
                        Chaque grille: [batch, H, W]
        
        Returns:
            Dict contenant:
            - category: str - Catégorie prédite
            - confidence: float - Confiance de la prédiction
            - pattern_features: torch.Tensor - Features des patterns détectés
            - pattern_maps: Dict - Cartes de patterns pour chaque type
        """
        if not train_pairs:
            raise ValueError("Au moins une paire train est requise")
        
        batch_size = train_pairs[0][0].shape[0]
        num_examples = len(train_pairs)
        
        # Encoder toutes les grilles input et output
        input_features_list = []
        output_features_list = []
        all_pattern_maps = {}
        
        for input_grid, output_grid in train_pairs:
            # Encoder input
            input_feat, input_patterns = self.encode_grid(input_grid)
            input_features_list.append(input_feat)
            
            # Encoder output
            output_feat, output_patterns = self.encode_grid(output_grid)
            output_features_list.append(output_feat)
            
            # Accumuler les pattern maps
            for ptype, pmap in input_patterns.items():
                if ptype not in all_pattern_maps:
                    all_pattern_maps[ptype] = []
                all_pattern_maps[ptype].append(pmap)
        
        # Stack les features de tous les exemples
        input_features = torch.stack(input_features_list, dim=1)  # [batch, num_examples, H, W, D]
        output_features = torch.stack(output_features_list, dim=1)
        
        # Analyse multi-exemples pour détecter les patterns communs
        pattern_features = self.multi_example_encoder(input_features, output_features)
        
        # Catégorisation
        category_result = self.categorizer(pattern_features)
        
        # Moyenner les pattern maps sur tous les exemples
        averaged_pattern_maps = {}
        for ptype, pmaps in all_pattern_maps.items():
            averaged_pattern_maps[ptype] = torch.stack(pmaps, dim=1).mean(dim=1)
        
        # Récupérer la catégorie prédite
        predicted_idx = category_result['predicted_category'][0].item()
        predicted_category = self.categorizer.categories[predicted_idx]
        confidence = category_result['category_probs'][0, predicted_idx].item()
        
        return {
            'category': predicted_category,
            'confidence': confidence,
            'pattern_features': pattern_features,
            'pattern_maps': averaged_pattern_maps,
            'category_logits': category_result['category_logits'],
            'category_probs': category_result['category_probs']
        }


# Fonctions utilitaires pour l'entraînement contrastif
class ContrastiveLoss(nn.Module):
    """Perte contrastive pour l'apprentissage de représentations."""
    def __init__(self, temperature=0.07):
        super().__init__()
        self.temperature = temperature
        
    def forward(self, features1, features2):
        # Normalisation des caractéristiques
        features1 = F.normalize(features1, dim=-1)
        features2 = F.normalize(features2, dim=-1)
        
        # Matrice de similarité
        sim_matrix = torch.einsum('bd,cd->bc', features1, features2) / self.temperature
        
        # Étiquettes positives sur la diagonale
        labels = torch.arange(features1.size(0), device=features1.device)
        
        # Perte contrastive
        loss = F.cross_entropy(sim_matrix, labels)
        return loss


def apply_augmentation(grid, aug_type='rotation'):
    """
    Applique des augmentations aux grilles pour l'entraînement contrastif.
    
    Args:
        grid: [batch, H, W]
        aug_type: Type d'augmentation ('rotation', 'flip', 'color_permutation')
    
    Returns:
        Grille augmentée de même forme
    """
    if aug_type == 'rotation':
        # Rotation de 90, 180, ou 270 degrés
        k = int(torch.randint(1, 4, (1,)).item())
        return torch.rot90(grid, k, dims=[1, 2])
    
    elif aug_type == 'flip':
        # Flip horizontal ou vertical
        if torch.rand(1) > 0.5:
            return torch.flip(grid, dims=[1])  # Flip vertical
        else:
            return torch.flip(grid, dims=[2])  # Flip horizontal
    
    elif aug_type == 'color_permutation':
        # Permutation des couleurs (préserve la structure)
        unique_colors = torch.unique(grid)
        if len(unique_colors) > 1:
            perm = torch.randperm(len(unique_colors))
            color_map = {unique_colors[i].item(): unique_colors[perm[i]].item() 
                        for i in range(len(unique_colors))}
            
            augmented = grid.clone()
            for old_color, new_color in color_map.items():
                augmented[grid == old_color] = new_color
            return augmented
    
    return grid  # Pas d'augmentation