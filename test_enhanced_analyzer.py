#!/usr/bin/env python3
"""
Script de test pour l'ARCAnalyzer amélioré
"""

import os
import json
from analyze_grid.arc_analyzer import ARCAnaly<PERSON>

def test_enhanced_analyzer():
    """Test de l'ARCAnalyzer avec les nouvelles fonctionnalités"""
    
    # Test avec l'exemple du code
    puzzle_example = {
        'train': [
            {
                'input': [[0, 7, 7], [7, 7, 7], [0, 7, 7]],
                'output': [[0, 0, 0, 0, 7, 7, 0, 7, 7], [0, 0, 0, 7, 7, 7, 7, 7, 7], [0, 0, 0, 0, 7, 7, 0, 7, 7], [0, 7, 7, 0, 7, 7, 0, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7], [0, 7, 7, 0, 7, 7, 0, 7, 7], [0, 0, 0, 0, 7, 7, 0, 7, 7], [0, 0, 0, 7, 7, 7, 7, 7, 7], [0, 0, 0, 0, 7, 7, 0, 7, 7]]
            }
        ],
        'test': []
    }

    analyzer = ARCAnalyzer()
    analysis = analyzer.analyze_puzzle(puzzle_example)

    print('=== NOUVELLES FONCTIONNALITÉS ===')
    print('Clés d\'analyse disponibles:')
    for key in sorted(analysis.keys()):
        print(f'- {key}')

    print('\n=== ANALYSE DES DIFFÉRENCES ===')
    if 'diff_analysis' in analysis:
        diff_data = analysis['diff_analysis']
        print(f'Exemples compatibles: {diff_data.get("compatible_examples", 0)}/{diff_data.get("total_examples", 0)}')
        print(f'Changements moyens: {diff_data.get("average_changes", 0):.1f}')

        if diff_data.get('train_diffs'):
            first_diff = diff_data['train_diffs'][0]
            if first_diff.get('compatible'):
                print(f'Premier exemple - Changements: {first_diff.get("total_changes", 0)}')
                print(f'Pourcentage modifié: {first_diff.get("change_percentage", 0):.1f}%')

    print('\n=== OBJETS AVEC REGIONPROPS ===')
    if 'enhanced_objects' in analysis:
        enhanced = analysis['enhanced_objects']
        stats = enhanced.get('object_statistics', {})
        print(f'Objets input: {stats.get("total_input_objects", 0)}')
        print(f'Objets output: {stats.get("total_output_objects", 0)}')
        print(f'Aire moyenne: {stats.get("average_object_area", 0):.1f}')

        if enhanced.get('train_objects'):
            first_example = enhanced['train_objects'][0]
            input_objs = first_example.get('input_objects', [])
            output_objs = first_example.get('output_objects', [])
            print(f'Premier exemple - Input: {len(input_objs)} objets, Output: {len(output_objs)} objets')

            if input_objs:
                obj = input_objs[0]
                print(f'Premier objet input - Aire: {obj.get("area", 0)}, Couleur: {obj.get("color", 0)}')
                print(f'  Excentricité: {obj.get("eccentricity", 0):.3f}, Solidité: {obj.get("solidity", 0):.3f}')

    print('\n=== STRUCTURE AMÉLIORÉE ===')
    try:
        enhanced_structure = analyzer.get_enhanced_analysis_structure(analysis)
        print('Structure JSON améliorée créée avec succès!')
        print(f'Version de l\'analyseur: {enhanced_structure["metadata"]["analyzer_version"]}')
        print(f'Fonctionnalités: {enhanced_structure["metadata"]["features"]}')
        print(f'Type de transformation principal: {enhanced_structure["summary"]["primary_transformation_type"]}')
        print(f'Score de confiance: {enhanced_structure["summary"]["confidence_score"]}')

        # Test de sérialisation JSON
        json_str = json.dumps(enhanced_structure, indent=2)
        print(f'\nSérialisation JSON réussie! Taille: {len(json_str)} caractères')

    except Exception as e:
        print(f'Erreur lors de la création de la structure améliorée: {e}')

    print('\n=== TEST RÉUSSI ===')

if __name__ == "__main__":
    test_enhanced_analyzer()
