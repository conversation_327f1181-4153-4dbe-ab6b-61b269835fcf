#!/usr/bin/env python3
"""
Script de test pour l'ARCAnalyzer amélioré
"""

import os
import json
from analyze_grid.arc_analyzer import ARCAnal<PERSON>zer

def test_enhanced_analyzer():
    """Test de l'ARCAnalyzer avec les nouvelles fonctionnalités"""
    
    # Test avec les fichiers JSON du répertoire
    puzzle_files = [f for f in os.listdir('.') if f.endswith('.json') and '_analysis' not in f]
    
    for filename in puzzle_files:
        print(f'\n=== ANALYSE DU FICHIER: {filename} ===')
        with open(filename, 'r') as f:
            puzzle_data = json.load(f)
        
        if 'train' in puzzle_data:
            analyzer = ARCAnalyzer()
            analysis = analyzer.analyze_puzzle(puzzle_data)
            
            print('=== Clés d\'analyse disponibles ===')
            for key in sorted(analysis.keys()):
                print(f'- {key}')
            
            if 'diff_analysis' in analysis:
                diff_data = analysis['diff_analysis']
                print(f'Exemples compatibles: {diff_data.get("compatible_examples", 0)}/{diff_data.get("total_examples", 0)}')
            
            if 'enhanced_objects' in analysis:
                enhanced = analysis['enhanced_objects']
                stats = enhanced.get('object_statistics', {})
                print(f'Objets input: {stats.get("total_input_objects", 0)}')
                print(f'Objets output: {stats.get("total_output_objects", 0)}')
            
            try:
                enhanced_structure = analyzer.get_enhanced_analysis_structure(analysis)
                print(f'Version de l\'analyseur: {enhanced_structure["metadata"]["analyzer_version"]}')
                print(f'Type de transformation principal: {enhanced_structure["summary"]["primary_transformation_type"]}')
                print(f'Score de confiance: {enhanced_structure["summary"]["confidence_score"]}')
                
                json_str = json.dumps(enhanced_structure, indent=2)
                print(f'Sérialisation JSON réussie! Taille: {len(json_str)} caractères')
                
            except Exception as e:
                print(f'Erreur lors de la création de la structure améliorée: {e}')
        else:
            print(f"Erreur: Le fichier {filename} ne contient pas de données 'train'")
    
    print('\n=== FIN DE L\'ANALYSE ===')

if __name__ == "__main__":
    test_enhanced_analyzer()
