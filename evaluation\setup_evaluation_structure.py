#!/usr/bin/env python3
"""
Setup script to ensure evaluation folder has proper structure for AGI files and metadata.
Creates directories and validates the evaluation dataset structure.
"""

import json
from pathlib import Path
import shutil
from typing import Dict, List
import argparse

def setup_evaluation_structure(base_dir: str = "../arcdata/evaluation"):
    """
    Set up the evaluation folder structure similar to training folder.
    
    Args:
        base_dir: Base directory for evaluation data
    """
    base_path = Path(base_dir)
    
    print("🔧 SETTING UP EVALUATION FOLDER STRUCTURE")
    print("=" * 50)
    print(f"Base directory: {base_path}")
    
    # Create necessary subdirectories
    subdirs = [
        "agi_files",      # Generated AGI scenario files
        "metadata",       # Metadata for each puzzle
        "analysis",       # Analysis results
        "reports",        # Evaluation reports
        "comparisons"     # Model comparison results
    ]
    
    created_dirs = []
    for subdir in subdirs:
        dir_path = base_path / subdir
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
            created_dirs.append(str(dir_path))
            print(f"✅ Created directory: {dir_path}")
        else:
            print(f"📁 Directory exists: {dir_path}")
    
    # Validate evaluation dataset
    json_files = list(base_path.glob("*.json"))
    valid_puzzles = []
    invalid_puzzles = []
    
    print(f"\n🔍 VALIDATING EVALUATION DATASET")
    print(f"Found {len(json_files)} JSON files")
    
    for json_file in json_files:
        if len(json_file.stem) == 8 and all(c in '0123456789abcdef' for c in json_file.stem.lower()):
            try:
                with open(json_file, 'r') as f:
                    puzzle_data = json.load(f)
                
                # Validate puzzle structure
                if validate_puzzle_structure(puzzle_data):
                    valid_puzzles.append(json_file.stem)
                else:
                    invalid_puzzles.append(json_file.stem)
            except Exception as e:
                invalid_puzzles.append(f"{json_file.stem} (error: {e})")
        else:
            print(f"⚠️ Skipping non-puzzle file: {json_file.name}")
    
    print(f"✅ Valid puzzles: {len(valid_puzzles)}")
    if invalid_puzzles:
        print(f"❌ Invalid puzzles: {len(invalid_puzzles)}")
        for invalid in invalid_puzzles[:5]:  # Show first 5
            print(f"   - {invalid}")
        if len(invalid_puzzles) > 5:
            print(f"   ... and {len(invalid_puzzles) - 5} more")
    
    # Create README file
    create_evaluation_readme(base_path, len(valid_puzzles), len(invalid_puzzles))
    
    # Summary
    print(f"\n📊 SETUP SUMMARY")
    print(f"- Base directory: {base_path}")
    print(f"- Created directories: {len(created_dirs)}")
    print(f"- Valid puzzles: {len(valid_puzzles)}")
    print(f"- Ready for evaluation: ✅")
    
    return {
        "base_directory": str(base_path),
        "created_directories": created_dirs,
        "valid_puzzles": len(valid_puzzles),
        "invalid_puzzles": len(invalid_puzzles),
        "puzzle_ids": valid_puzzles
    }

def validate_puzzle_structure(puzzle_data: Dict) -> bool:
    """
    Validate that a puzzle has the required structure.
    
    Args:
        puzzle_data: Puzzle data dictionary
        
    Returns:
        True if valid, False otherwise
    """
    try:
        # Check required fields
        if 'train' not in puzzle_data or 'test' not in puzzle_data:
            return False
        
        # Check train examples
        train_examples = puzzle_data['train']
        if not isinstance(train_examples, list) or len(train_examples) == 0:
            return False
        
        for example in train_examples:
            if 'input' not in example or 'output' not in example:
                return False
            if not isinstance(example['input'], list) or not isinstance(example['output'], list):
                return False
        
        # Check test examples
        test_examples = puzzle_data['test']
        if not isinstance(test_examples, list) or len(test_examples) == 0:
            return False
        
        for example in test_examples:
            if 'input' not in example:
                return False
            if not isinstance(example['input'], list):
                return False
            # Note: 'output' may not be present in evaluation dataset
        
        return True
        
    except Exception:
        return False

def create_evaluation_readme(base_path: Path, valid_puzzles: int, invalid_puzzles: int):
    """Create README file for evaluation structure."""
    readme_content = f"""# ARC Evaluation Dataset Structure

This directory contains the ARC evaluation dataset and generated results.

## Directory Structure

```
{base_path.name}/
├── *.json              # Original ARC evaluation puzzles ({valid_puzzles} valid puzzles)
├── agi_files/          # Generated AGI scenario files (.agi)
├── metadata/           # Metadata for each puzzle (JSON)
├── analysis/           # Analysis results and statistics
├── reports/            # Evaluation reports (Markdown)
└── comparisons/        # Model comparison results
```

## Generated Files

### AGI Scenario Files (`agi_files/`)
- Format: `{{puzzle_id}}.agi`
- Contains generated programs for each puzzle
- Similar structure to training folder AGI files

### Metadata Files (`metadata/`)
- Format: `{{puzzle_id}}_metadata.json`
- Contains generation details, validation results, timing info

### Reports (`reports/`)
- Model evaluation reports
- Performance statistics
- Error analysis

### Comparisons (`comparisons/`)
- Multi-model comparison results
- Performance benchmarks

## Dataset Statistics

- **Total JSON files found:** {valid_puzzles + invalid_puzzles}
- **Valid puzzles:** {valid_puzzles}
- **Invalid puzzles:** {invalid_puzzles}

## Usage

1. **Single Model Evaluation:**
   ```bash
   python evaluation/evaluate_trained_models_on_evaluation.py --model path/to/model.pth
   ```

2. **Multi-Model Evaluation:**
   ```bash
   python evaluation/evaluate_trained_models_on_evaluation.py --models model1.pth model2.pth
   ```

3. **Custom Dataset:**
   ```bash
   python evaluation/evaluate_trained_models_on_evaluation.py --dataset path/to/dataset
   ```

## Generated by Setup Script

This structure was created by `setup_evaluation_structure.py` to ensure
proper organization of evaluation results and compatibility with the
training folder structure.
"""
    
    readme_file = base_path / "README.md"
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"📝 Created README: {readme_file}")

def clean_evaluation_results(base_dir: str = "../arcdata/evaluation"):
    """Clean generated evaluation results (keep original puzzles)."""
    base_path = Path(base_dir)
    
    print("🧹 CLEANING EVALUATION RESULTS")
    print("=" * 40)
    
    # Directories to clean
    clean_dirs = ["agi_files", "metadata", "analysis", "reports", "comparisons"]
    
    for clean_dir in clean_dirs:
        dir_path = base_path / clean_dir
        if dir_path.exists():
            shutil.rmtree(dir_path)
            print(f"🗑️ Removed: {dir_path}")
        else:
            print(f"📁 Not found: {dir_path}")
    
    # Remove README if it exists
    readme_file = base_path / "README.md"
    if readme_file.exists():
        readme_file.unlink()
        print(f"🗑️ Removed: {readme_file}")
    
    print("✅ Cleanup completed")

def main():
    """Main function for setup script."""
    parser = argparse.ArgumentParser(description="Setup evaluation folder structure")
    parser.add_argument("--base-dir", default="../arcdata/evaluation",
                       help="Base directory for evaluation data")
    parser.add_argument("--clean", action="store_true",
                       help="Clean generated results (keep original puzzles)")
    
    args = parser.parse_args()
    
    if args.clean:
        clean_evaluation_results(args.base_dir)
    else:
        setup_evaluation_structure(args.base_dir)

if __name__ == "__main__":
    main()
